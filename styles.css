/* 基础样式重置 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Microsoft YaHei', '微软雅黑', Arial, sans-serif;
    line-height: 1.5;
    color: #333;
    background: #f5f7fa;
    min-height: 100vh;
    position: relative;
}

.container {
    max-width: 1000px;
    margin: 0 auto;
    padding: 20px;
    min-height: 100vh;
}

/* 全局文件选择器 */
.global-file-selector {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 1000;
}

.global-file-selector .file-input {
    position: absolute;
    opacity: 0;
    width: 100%;
    height: 100%;
    cursor: pointer;
    pointer-events: none;
}

.global-file-selector .upload-btn {
    background: #3498db;
    color: white;
    border: none;
    padding: 10px 20px;
    border-radius: 6px;
    font-size: 0.9rem;
    cursor: pointer;
    transition: all 0.3s ease;
    font-weight: 500;
    box-shadow: 0 2px 8px rgba(52, 152, 219, 0.3);
}

.global-file-selector .upload-btn:hover {
    background: #2980b9;
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(52, 152, 219, 0.4);
}

/* 文件信息显示（全局） */
.file-info {
    position: fixed;
    top: 70px;
    right: 20px;
    z-index: 1000;
    background: #e8f5e8;
    border-radius: 6px;
    padding: 15px 20px;
    border-left: 4px solid #27ae60;
    max-width: 280px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

/* 主内容区域 */
.main-content {
    background: white;
    border-radius: 12px;
    padding: 30px;
    box-shadow: 0 4px 20px rgba(0,0,0,0.08);
    border: 1px solid #e1e8ed;
}



.file-details {
    margin-bottom: 10px;
}

.file-name {
    display: block;
    font-weight: 600;
    color: #2c3e50;
    font-size: 0.9rem;
    margin-bottom: 4px;
}

.file-meta {
    font-size: 0.8rem;
    color: #7f8c8d;
}

.reprocess-btn {
    background: #95a5a6;
    color: white;
    border: none;
    padding: 6px 12px;
    border-radius: 4px;
    font-size: 0.8rem;
    cursor: pointer;
    transition: all 0.3s ease;
    width: 100%;
}

.reprocess-btn:hover {
    background: #7f8c8d;
}

/* 消息区域 */
.message {
    padding: 12px 16px;
    border-radius: 6px;
    margin-top: 20px;
    display: none;
    font-size: 0.9rem;
}

.message.success {
    background: #d4edda;
    color: #155724;
    border: 1px solid #c3e6cb;
    display: block;
}

.message.error {
    background: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
    display: block;
}

.message.info {
    background: #d1ecf1;
    color: #0c5460;
    border: 1px solid #bee5eb;
    display: block;
}

/* 进度条样式 */
.progress-container {
    margin: 10px 0;
    background-color: #f0f0f0;
    border-radius: 10px;
    overflow: hidden;
    height: 20px;
    display: none;
}

.progress-bar {
    height: 100%;
    background: linear-gradient(90deg, #4CAF50, #45a049);
    width: 0%;
    transition: width 0.3s ease;
    border-radius: 10px;
    position: relative;
}

.progress-text {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: white;
    font-size: 12px;
    font-weight: bold;
    text-shadow: 1px 1px 1px rgba(0,0,0,0.5);
}

/* 月视图样式 */
.month-view-section {
    margin-top: 25px;
    padding: 20px;
    background: #f8fafe;
    border-radius: 8px;
    border: 1px solid #e1e8ed;
}

.month-view-header {
    margin-bottom: 15px;
}

.stats-summary {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;
    padding: 10px 16px;
    background: white;
    border-radius: 6px;
    border: 1px solid #e1e8ed;
    font-size: 0.9rem;
}

/* 导航和设置行 */
.navigation-settings-row {
    display: flex;
    justify-content: flex-start;
    align-items: center;
    gap: 15px;
    flex-wrap: wrap;
    margin-right: 320px; /* 为文件信息区域留出空间 */
}

.stats-item {
    color: #2c3e50;
}

.stats-item strong {
    color: #3498db;
}

.selected-count {
    color: #e74c3c;
}

/* 月历导航 */
.calendar-navigation {
    display: flex;
    align-items: center;
    justify-content: flex-start;
    gap: 15px;
}

.nav-btn {
    background: #3498db;
    color: white;
    border: none;
    width: 32px;
    height: 32px;
    border-radius: 4px;
    font-size: 1rem;
    cursor: pointer;
    transition: all 0.3s ease;
}

.nav-btn:hover {
    background: #2980b9;
}

.current-month {
    font-size: 1.1rem;
    font-weight: 600;
    color: #2c3e50;
    min-width: 100px;
    text-align: center;
}

.clear-btn {
    background: #e74c3c;
    color: white;
    border: none;
    padding: 6px 12px;
    border-radius: 4px;
    font-size: 0.85rem;
    cursor: pointer;
    transition: all 0.3s ease;
    white-space: nowrap; /* 防止换行 */
    flex-shrink: 0; /* 防止在flex容器中被压缩 */
}

.clear-btn:hover {
    background: #c0392b;
}

/* 自动选择设置 */
.auto-select-settings {
    display: flex;
    align-items: center;
    justify-content: flex-start;
    gap: 6px;
    padding: 8px 10px;
    background: #f8f9fa;
    border-radius: 6px;
    border: 1px solid #e9ecef;
    font-size: 0.85rem;
    max-width: 280px;
    flex-shrink: 1;
    margin-left: 15px; /* 与月份导航保持间距 */
    margin-right: 15px; /* 与清除按钮保持间距 */
    white-space: nowrap; /* 防止换行 */
    flex-wrap: nowrap; /* 不允许子元素换行 */
}

.auto-select-settings label {
    color: #495057;
    font-weight: 500;
}

.days-input {
    width: 60px;
    padding: 4px 8px;
    border: 1px solid #ced4da;
    border-radius: 4px;
    text-align: center;
    font-size: 0.9rem;
}

.days-input:focus {
    outline: none;
    border-color: #3498db;
    box-shadow: 0 0 0 2px rgba(52, 152, 219, 0.2);
}

.save-settings-btn {
    background: #28a745;
    color: white;
    border: none;
    padding: 4px 12px;
    border-radius: 4px;
    font-size: 0.85rem;
    cursor: pointer;
    transition: all 0.3s ease;
}

.save-settings-btn:hover {
    background: #218838;
}

.settings-status {
    font-size: 0.8rem;
    color: #28a745;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.settings-status.show {
    opacity: 1;
}

/* 月历容器 */
.calendar-container {
    background: white;
    border-radius: 6px;
    padding: 15px;
    border: 1px solid #e1e8ed;
    margin-bottom: 15px;
}

/* 月历表格 */
.calendar-table {
    width: 100%;
    border-collapse: collapse;
    font-size: 0.9rem;
}

.calendar-table th,
.calendar-table td {
    width: 14.28%;
    height: 40px;
    text-align: center;
    vertical-align: middle;
    border: 1px solid #f1f3f4;
    position: relative;
}

.calendar-table th {
    background: #f8f9fa;
    font-weight: 600;
    color: #495057;
    height: 35px;
    font-size: 0.85rem;
}

.calendar-date {
    cursor: pointer;
    transition: all 0.2s ease;
    border-radius: 3px;
    margin: 1px;
    height: 36px;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    font-size: 0.9rem;
}

.calendar-date:hover {
    background: #e8f4fd;
}

.calendar-date.other-month {
    color: #bdc3c7;
    cursor: default;
}

.calendar-date.other-month:hover {
    background: transparent;
}

.calendar-date.has-data {
    background: #e8f5e8;
    font-weight: 600;
    color: #27ae60;
}

.calendar-date.has-data:hover {
    background: #d5e7d5;
}

.calendar-date.no-data {
    background: #f8f9fa;
    color: #adb5bd;
    font-weight: 400;
    cursor: not-allowed !important;
    opacity: 0.6;
}

.calendar-date.no-data:hover {
    background: #f8f9fa !important;
    color: #adb5bd !important;
}

.calendar-date.selected {
    background: #3498db !important;
    color: white;
    font-weight: 600;
}

.calendar-date.selected:hover {
    background: #2980b9 !important;
}

/* 底部区域 */
.bottom-section {
    display: flex;
    gap: 20px;
    align-items: flex-start;
}

.selected-dates {
    flex: 1;
    background: white;
    border-radius: 6px;
    padding: 15px;
    border: 1px solid #e1e8ed;
}

.selected-dates-list {
    min-height: 40px;
    max-height: 120px;
    overflow-y: auto;
}

.no-selection {
    color: #95a5a6;
    font-size: 0.9rem;
    text-align: center;
    margin: 0;
    padding: 10px 0;
}

.selected-date-item {
    display: inline-block;
    background: #3498db;
    color: white;
    padding: 4px 10px;
    margin: 2px;
    border-radius: 12px;
    font-size: 0.85rem;
    position: relative;
}

.selected-date-item .remove-date {
    margin-left: 6px;
    cursor: pointer;
    font-weight: bold;
    opacity: 0.8;
}

.selected-date-item .remove-date:hover {
    opacity: 1;
}

/* 操作按钮 */
.action-buttons {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.action-btn {
    padding: 10px 20px;
    border: none;
    border-radius: 6px;
    font-size: 0.9rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    min-width: 120px;
}

.preview-btn {
    background: #f39c12;
    color: white;
}

.preview-btn:hover {
    background: #e67e22;
}

.export-btn {
    background: #27ae60;
    color: white;
}

.export-btn:hover:not(:disabled) {
    background: #229954;
}

.export-btn:disabled {
    background: #95a5a6;
    cursor: not-allowed;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .container {
        padding: 15px;
    }

    .main-content {
        padding: 20px;
    }

    .global-file-selector {
        top: 15px;
        right: 15px;
    }

    .global-file-selector .upload-btn {
        padding: 8px 16px;
        font-size: 0.85rem;
    }

    .file-info {
        top: 60px;
        right: 15px;
        max-width: 250px;
        padding: 12px 16px;
    }

    .stats-summary {
        flex-direction: column;
        gap: 8px;
        text-align: center;
    }

    .navigation-settings-row {
        flex-direction: column;
        gap: 15px;
        align-items: stretch;
        margin-right: 0; /* 在小屏幕上移除右边距 */
    }

    .calendar-navigation {
        justify-content: center;
        flex-wrap: wrap;
        gap: 10px;
    }

    .auto-select-settings {
        justify-content: center;
    }

    .current-month {
        order: -1;
        width: 100%;
    }

    .calendar-table th,
    .calendar-table td {
        height: 35px;
        font-size: 0.8rem;
    }

    .calendar-date {
        height: 30px;
        font-size: 0.8rem;
    }

    .bottom-section {
        flex-direction: column;
        gap: 15px;
    }

    .action-buttons {
        flex-direction: row;
        justify-content: center;
    }

    .action-btn {
        min-width: 100px;
    }
}

/* 动画效果 */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(15px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.file-info,
.month-view-section {
    animation: fadeIn 0.4s ease;
}
